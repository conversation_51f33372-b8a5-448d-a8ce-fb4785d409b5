import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { <PERSON><PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { LucideAngularModule } from 'lucide-angular';
import { PaginationControlsComponent } from '../../components/pagination-controls/pagination-controls.component';
import { IconComponent } from '../../components/icon/icon.component';
import { ButtonComponent } from '../../components/button/button.component';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';
import { CheckboxComponent, DropdownComponent } from '../../../public-api';

export interface DropdownOption {
  name: string;
  value: string;
}

export interface FieldWithIcon {
  value: string;
  iconName: string;
  clickable: boolean;
}

export interface ActionConfig {
  enabled: boolean;
  label: string;
  icon: string;
  inline: boolean;
}

export interface IconRowData {
  id?: string;
  parentId?: string | null;
  name: FieldWithIcon;
  email: FieldWithIcon;
  access: FieldWithIcon;
  addedOn: FieldWithIcon;
  validtill: FieldWithIcon;
  lastLogin: FieldWithIcon;
  authorized: FieldWithIcon;
  action: Record<string, ActionConfig>;
  sortOrder: number;
  isSelected?: boolean;
}

// For convenience
export type User = IconRowData;
export type RowDataKey = keyof IconRowData;

// Used for defining column display settings
export interface ColumnConfig {
  field: RowDataKey | 'actions' | 'select'; // 'actions' is handled separately in UI
  label: string;
  sortable: boolean;
  filterable: boolean;
  sortingOrder?: number;
  visible: boolean;
  resizable?: boolean;
}

@Component({
  selector: 'ava-user-data-table',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    LucideAngularModule,
    IconComponent,
    ButtonComponent,
    AvaTextboxComponent,
    DropdownComponent,
    CheckboxComponent,
    PaginationControlsComponent,
  ],
  templateUrl: './user-data-table.component.html',
  styleUrl: './user-data-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserDataTableComponent implements OnInit, AfterViewInit {
  @Input() columns: ColumnConfig[] = [];
  @Input() data: User[] = [];
  @Input() rowDrag = false;
  @Input() columnDrag = false;
  @Input() useDefaultFilter = false;
  @Input() resizable = false;
  @Input() sortAscIcon: string = 'arrow-up';
  @Input() sortDescIcon: string = 'arrow-down';
  @Input() filterIcon: string = 'filter';
  @Input() showCheckbox = false;
  @Input() useCustomFilter = false;
  @Input() currentPage = 1;
  @Input() pageSize = 5;
  displayedRows: any[] = [];

  @ViewChild('tableRef', { static: false }) tableRef?: ElementRef;
  @ViewChild('columnPanel') columnPanel!: ElementRef;
  @ViewChild('actualTable') actualTable!: ElementRef;
  @ViewChild('wrapper', { static: false }) wrapper?: ElementRef;
  @ViewChild('draggableWrapper') draggableWrapper!: ElementRef;

  @Output() cellClick = new EventEmitter<{ row: User; field: string }>();
  @Output() actionTriggered = new EventEmitter<{
    row: User;
    actionKey: string;
    config: ActionConfig;
  }>();
  @Output() columnOrderChanged = new EventEmitter<ColumnConfig[]>();
  @Output() rowOrderChanged = new EventEmitter<User[]>();
  @Output() selectionChanged = new EventEmitter<User[]>();
  @Output() selectedRowsChange = new EventEmitter<any[]>();
  paginatedData: any[] = [];
  //sorting
  hoveredCol: string | null = null;
  sortDirection: 'asc' | 'desc' | '' = '';
  sortColumn: keyof User | '' = '';
  dropdownRow: User | null = null;
  //originalData: User[] = [];
  filteredData: User[] = [];
  //filter
  filterDropdownPosition: { x: number; y: number } | null = null;
  tempSelectedFilters: { [key: string]: Set<string> } = {};
  columnFilters: { [key: string]: string[] } = {};
  activeFilters: { [key: string]: Set<string> } = {};
  openFilterField: string | null = null;
  defaultColumnFilters: {
    [field: string]: {
      value: string;
      type: string;
    };
  } = {};
  defaultFilterConditions: DropdownOption[] = [
    { name: 'Starts With', value: 'Starts With' },
    { name: 'Ends With', value: 'Ends With' },
    { name: 'Contains', value: 'Contains' },
    { name: 'Equal', value: 'Equal' },
    { name: 'Empty', value: 'Empty' },
    { name: 'Does Not Start With', value: 'Does Not Start With' },
    { name: 'Does Not End With', value: 'Does Not End With' },
    { name: 'Does Not Contain', value: 'Does Not Contain' },
    { name: 'Not Equal', value: 'Not Equal' },
    { name: 'Not Empty', value: 'Not Empty' },
  ];

  //drag and drop
  page = 0;
  rowsPerPage = 10;
  dragStartRowIndex: number | null = null;
  dragOverRowIndex: number | null = null;
  dragStartColIndex: number | null = null;
  dragOverColIndex: number | null = null;

  //checkbox
  parentChecked = false;
  indeterminate = false;
  selectedRows: User[] = [];
  indeterminateStates: { [key: string]: boolean } = {};

  //Icon drag
  showColumnPanel = false;
  columnSearch = '';
  draggableIconTop = 50;
  isDragging = false;
  startY = 0;
  startTop = 0;
  dragging = false;

  constructor(private sanitizer: DomSanitizer, private elRef: ElementRef) {}

  ngOnInit() {
    this.paginatedData = this.data;
    //this.totalPages = this.data.length;
    this.updatePaginatedData();
    this.applyDefaultFilter();
    this.extractColumnFilterOptions(this.data);
  }

  ngAfterViewInit() {
    const iconEl = this.draggableWrapper?.nativeElement as HTMLElement;
    const container = this.wrapper?.nativeElement as HTMLElement;

    const savedTop = localStorage.getItem('draggableIconTop');
    if (savedTop) {
      this.draggableIconTop = parseInt(savedTop, 10);
      iconEl.style.top = `${this.draggableIconTop}px`;
    }

    iconEl.addEventListener('mousedown', (e: MouseEvent) => {
      this.isDragging = true;
      this.startY = e.clientY;
      this.startTop = iconEl.offsetTop;
      iconEl.style.cursor = 'grabbing';
      e.preventDefault();
    });

    window.addEventListener('mousemove', (e: MouseEvent) => {
      if (!this.isDragging) return;

      const deltaY = e.clientY - this.startY;
      let newTop = this.startTop + deltaY;

      const maxTop = container.clientHeight - iconEl.offsetHeight;
      newTop = Math.max(0, Math.min(newTop, maxTop));

      this.draggableIconTop = newTop;
      iconEl.style.top = `${newTop}px`;
    });

    window.addEventListener('mouseup', () => {
      if (this.isDragging) {
        this.isDragging = false;
        iconEl.style.cursor = 'grab';
        localStorage.setItem(
          'draggableIconTop',
          this.draggableIconTop.toString()
        );
      }
    });
  }
  //pagination
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updatePaginatedData();
  }

  get totalPages(): number {
    return Math.ceil(this.data.length / this.pageSize);
  }

  updatePaginatedData(): void {
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    this.displayedRows = this.data.slice(start, end);
  }
  //Sorting
  onSort(col: ColumnConfig): void {
    if (!col.sortable || col.field === 'actions') return;
    this.sortColumn = col.field as keyof User;
    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    this.applySortingAndPagination();
  }

  applySortingAndPagination(): void {
    const baseData =
      this.filteredData.length > 0 ||
      Object.keys(this.defaultColumnFilters).length > 0
        ? this.filteredData
        : this.data;
    const sortedData = [...baseData];
    if (this.sortDirection && this.sortColumn) {
      const key = this.sortColumn;
      sortedData.sort((a, b) => {
        const aVal = this.getFieldValue(a[key]);
        const bVal = this.getFieldValue(b[key]);
        if (aVal == null) return 1;
        if (bVal == null) return -1;
        return this.sortDirection === 'asc'
          ? String(aVal).localeCompare(String(bVal))
          : String(bVal).localeCompare(String(aVal));
      });
    }
    const start = this.page * this.rowsPerPage;
    const end = start + this.rowsPerPage;
    this.paginatedData = sortedData.slice(start, end);
  }

  getFieldValue(field: any): string {
    if (field && typeof field === 'object' && 'value' in field) {
      return String(field.value);
    }
    return String(field ?? '');
  }

  //Start Filtering

  applyDefaultFilter(): void {
    const activeFields = Object.keys(this.defaultColumnFilters).filter(
      (field) => {
        const filter = this.defaultColumnFilters[field];
        return (
          filter.value.trim() !== '' || this.isFilterTypeEmpty(filter.type)
        );
      }
    );

    this.filteredData = this.data.filter((row) => {
      return activeFields.every((field) => {
        const { type, value } = this.defaultColumnFilters[field];
        const val = value.toLowerCase().trim();
        const rawCell = row[field as keyof User];

        if (
          rawCell &&
          typeof rawCell === 'object' &&
          'value' in rawCell &&
          typeof rawCell.value === 'string'
        ) {
          const cell = rawCell.value.toLowerCase();

          switch (type) {
            case 'Starts With':
              return cell.startsWith(val);
            case 'Ends With':
              return cell.endsWith(val);
            case 'Contains':
              return cell.includes(val);
            case 'Equal':
              return cell === val;
            case 'Empty':
              return cell === '';
            case 'Does Not Start With':
              return !cell.startsWith(val);
            case 'Does Not End With':
              return !cell.endsWith(val);
            case 'Does Not Contain':
              return !cell.includes(val);
            case 'Not Equal':
              return cell !== val;
            case 'Not Empty':
              return cell !== '';
            default:
              return true;
          }
        }

        return false;
      });
    });

    this.page = 0;
    this.paginatedData = this.filteredData.slice(
      this.page * this.rowsPerPage,
      (this.page + 1) * this.rowsPerPage
    );
  }

  applyAndCloseFilter(): void {
    this.applyDefaultFilter();
    this.openFilterField = null;
    this.filterDropdownPosition = null;
  }

  isFilterTypeEmpty(type: string): boolean {
    return type.toLowerCase() === 'empty' || type.toLowerCase() === 'not empty';
  }

  clearDefaultFilter(): void {
    if (!this.openFilterField) return;

    // Reset this column's filter values
    this.defaultColumnFilters[this.openFilterField] = {
      value: '',
      type: 'Starts With',
    };
    // Reset full data
    this.filteredData = [...this.data];
    // Close modal
    this.openFilterField = null;
    // Refresh view
    this.page = 0;
    this.applySortingAndPagination();
  }

  onFilterIconClick(event: MouseEvent, field: string) {
    event.stopPropagation();
    this.extractColumnFilterOptions(this.data);

    //Initialize default filter config if not exists
    if (!this.defaultColumnFilters[field]) {
      this.defaultColumnFilters[field] = { type: 'Starts With', value: '' };
    }

    // Extract filter options from full data if not initialized
    if (!this.columnFilters[field] || this.columnFilters[field].length === 0) {
      this.extractColumnFilterOptions(this.data);
    }

    // Ensure tempSelectedFilters initialized
    if (!this.tempSelectedFilters[field]) {
      this.tempSelectedFilters[field] = new Set();
    }

    //  Sync temp with active filters
    if (!this.activeFilters[field]) {
      this.activeFilters[field] = new Set();
    }
    this.tempSelectedFilters[field] = new Set(this.activeFilters[field]);

    //Set open field
    this.openFilterField = field;

    // Position modal
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    this.filterDropdownPosition = {
      x: rect.right,
      y: rect.bottom + window.scrollY,
    };
  }

  isFilterActive(field: string): boolean {
    const filter = this.defaultColumnFilters[field];
    return !!(filter && filter.value.trim().length > 0);
  }
  //End Filtering

  // Start column resizing
  resizing = {
    active: false,
    startX: 0,
    startWidth: 0,
    colIndex: -1,
  };
  onMouseMove = (event: MouseEvent) => {
    if (!this.resizing.active) return;

    const deltaX = event.pageX - this.resizing.startX;
    const ths = this.tableRef?.nativeElement.querySelectorAll('th');
    if (!ths || !ths[this.resizing.colIndex]) return;

    const th = ths[this.resizing.colIndex] as HTMLElement;
    th.style.width = `${this.resizing.startWidth + deltaX}px`;
  };

  onMouseUp = () => {
    if (!this.resizing.active) return;

    this.resizing.active = false;
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
  };

  onResizeMouseDown(event: MouseEvent, colIndex: number) {
    if (!this.resizable) return;
    event.preventDefault();
    this.resizing.active = true;
    this.resizing.startX = event.pageX;
    this.resizing.colIndex = colIndex;

    const th = (event.target as HTMLElement).closest('th')!;
    this.resizing.startWidth = th.offsetWidth;

    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);
  }
  // End column resizing

  //Start COLUMN DRAG EVENTS
  onColumnDragStart(event: DragEvent, colIndex: number) {
    this.dragStartColIndex = colIndex;
  }

  onColumnDragEnter(event: DragEvent, colIndex: number) {
    event.preventDefault();
    this.dragOverColIndex = colIndex;
  }

  onColumnDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onRowDragStart(event: DragEvent, rowIndex: number) {
    this.dragStartRowIndex = rowIndex;
  }

  onRowDragEnter(event: DragEvent, rowIndex: number) {
    event.preventDefault();
    this.dragOverRowIndex = rowIndex;
  }

  onRowDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onsRowDrop(event: DragEvent) {
    event.preventDefault();
    if (this.dragStartRowIndex === null || this.dragOverRowIndex === null)
      return;

    const start = this.page * this.rowsPerPage;
    const end = start + this.rowsPerPage;

    const dataSlice = [...this.paginatedData];
    const draggedItem = dataSlice.splice(this.dragStartRowIndex, 1)[0];
    dataSlice.splice(this.dragOverRowIndex, 0, draggedItem);

    // Update the main data array accordingly
    this.data.splice(start, this.rowsPerPage, ...dataSlice);

    this.applySortingAndPagination(); // Refresh paginatedData
    this.dragStartRowIndex = this.dragOverRowIndex = null;
  }

  onRowDrop(event: DragEvent) {
    event.preventDefault();

    if (this.dragStartRowIndex === null || this.dragOverRowIndex === null)
      return;

    const start = this.page * this.rowsPerPage;
    const fromIndex = start + this.dragStartRowIndex;
    const toIndex = start + this.dragOverRowIndex;

    if (fromIndex === toIndex) {
      this.dragStartRowIndex = null;
      this.dragOverRowIndex = null;
      return;
    }

    const movedItem = this.data.splice(fromIndex, 1)[0];
    this.data.splice(toIndex, 0, movedItem);

    this.data.forEach((item, index) => {
      item.sortOrder = index + 1;
    });

    const end = start + this.rowsPerPage;
    this.paginatedData = this.data.slice(start, end);

    this.rowOrderChanged.emit(this.data);

    this.dragStartRowIndex = null;
    this.dragOverRowIndex = null;
  }

  onColumnDrop(event: DragEvent) {
    event.preventDefault();

    if (this.dragStartColIndex === null || this.dragOverColIndex === null)
      return;

    const movedColumn = this.columns.splice(this.dragStartColIndex, 1)[0];
    this.columns.splice(this.dragOverColIndex, 0, movedColumn);

    this.columns.forEach((col, index) => {
      col.sortingOrder = index + 1;
    });

    this.columnOrderChanged.emit(this.columns);

    this.dragStartColIndex = null;
    this.dragOverColIndex = null;
  }

  //End COLUMN DRAG EVENTS

  //Start Icon
  getIcon(url: string | undefined): SafeHtml {
    if (!url) return '';
    const imgHTML = `<img src="${url}" width="16" height="16" style="margin-right: 4px; vertical-align: middle;" />`;
    return this.sanitizer.bypassSecurityTrustHtml(imgHTML);
  }

  getActionIconHtml(iconUrl: string | undefined): SafeHtml {
    if (!iconUrl) return '';
    const html = `<img src="${iconUrl}" width="16" height="16" style="vertical-align: middle;" />`;
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  // End Icon

  //Table cell Click
  handleCellClick(row: User, field: string) {
    console.log('Cell clicked:', row, field);
    this.cellClick.emit({ row, field });
  }

  getCellValue(row: any, field: string): string {
    if (field === 'action' || field === 'select') return ''; // return empty for select field
    return row[field]?.value || '';
  }

  getInlineActions(row: User): [string, ActionConfig][] {
    return Object.entries(row.action || {}).filter(
      ([, config]) => config.enabled && config.inline
    );
  }
  handleAction(row: User, actionKey: string): void {
    const config = row.action[actionKey];
    console.log(`Action triggered: ${config.label}`, row);
    this.dropdownRow = null;
    this.actionTriggered.emit({ row, actionKey, config });
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent): void {
    const clickedInside = this.tableRef?.nativeElement.contains(event.target);
    if (!clickedInside) {
      this.dropdownRow = null;
      this.openFilterField = null;
      this.filterDropdownPosition = null;
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscape(): void {
    this.dropdownRow = null;
  }

  shouldShowDropdown(row: User): boolean {
    return this.getDropdownActions(row).length > 0;
  }

  getDropdownActions(row: User): [string, ActionConfig][] {
    return Object.entries(row.action || {}).filter(
      ([, config]) => config.enabled && !config.inline
    );
  }

  toggleDropdown(row: User): void {
    this.dropdownRow = this.dropdownRow === row ? null : row;
  }

  isUrl(icon: string): boolean {
    return icon?.startsWith('http://') || icon?.startsWith('https://');
  }

  getSelectedFilterLabel(field: string): string {
    const type = this.defaultColumnFilters[field]?.type;
    const match = this.defaultFilterConditions.find(
      (option) => option.value === type
    );
    return match?.name || 'Filter Type';
  }

  //checkbox
  onSelectAllChange(checked: boolean): void {
    this.parentChecked = checked;
    this.indeterminate = false;
    this.paginatedData.forEach((row) => (row.isSelected = checked));
    this.emitSelectedRows();
  }

  onRowCheckboxChange(row: any, checked: boolean): void {
    row.isSelected = checked;
    this.updateSelectAllState();
    this.emitSelectedRows();
  }

  updateSelectAllState(): void {
    const total = this.paginatedData.length;
    const checkedCount = this.paginatedData.filter((r) => r.isSelected).length;

    if (checkedCount === total) {
      this.parentChecked = true;
      this.indeterminate = false;
    } else if (checkedCount === 0) {
      this.parentChecked = false;
      this.indeterminate = false;
    } else {
      this.parentChecked = false;
      this.indeterminate = true;
    }
  }

  emitSelectedRows(): void {
    const selectedRows = this.paginatedData.filter((row) => row.isSelected);
    this.selectedRowsChange.emit(selectedRows);
  }

  //custom filter
  updateFilter(): void {
    // 1. Sync temp → active
    Object.keys(this.tempSelectedFilters).forEach((field) => {
      this.activeFilters[field] = new Set(this.tempSelectedFilters[field]);
    });

    // 2. Apply filters
    this.applyActiveFilters(); // sets this.filteredData
    this.extractColumnFilterOptions(this.filteredData);

    // 3. Reset page and apply sorting/pagination
    this.page = 0;
    this.applySortingAndPagination();

    // 4. Close the dropdown
    this.openFilterField = null;
  }

  applyActiveFilters(): void {
    this.filteredData = this.data.filter((row) =>
      Object.entries(this.activeFilters).every(([field, selectedValues]) => {
        if (!selectedValues || selectedValues.size === 0) return true;

        const cell = row[field as keyof User];

        if (
          cell &&
          typeof cell === 'object' &&
          'value' in cell &&
          typeof cell.value === 'string'
        ) {
          return selectedValues.has(cell.value); // safe here
        }

        return false;
      })
    );
  }

  extractColumnFilterOptions(sourceData: User[]) {
    this.columns.forEach((col) => {
      if (col.filterable) {
        const values: string[] = sourceData
          .map((row) => {
            const fieldValue = row[col.field as keyof User];

            if (fieldValue) {
              if (
                typeof fieldValue === 'object' &&
                'value' in fieldValue &&
                typeof fieldValue.value === 'string'
              ) {
                return fieldValue.value;
              } else if (typeof fieldValue === 'string') {
                return fieldValue;
              }
            }
            return null;
          })
          .filter((val): val is string => Boolean(val)); // filter out nulls

        const unique = Array.from(new Set(values));
        this.columnFilters[col.field] = unique;

        if (!this.activeFilters[col.field]) {
          this.activeFilters[col.field] = new Set();
        }

        this.tempSelectedFilters[col.field] = new Set(
          this.activeFilters[col.field]
        );

        console.log(`Filter options for ${col.field}:`, unique);
      }
    });
  }

  //

  toggleFilterValue(field: string, value: string, isChecked: boolean): void {
    const selectedSet = this.tempSelectedFilters[field];
    if (isChecked) {
      selectedSet.add(value);
    } else {
      selectedSet.delete(value);
    }
    this.updateIndeterminateState(field);
  }

  toggleAllFilterValues(field: string, isChecked: boolean): void {
    const allValues = this.columnFilters[field];
    if (isChecked) {
      allValues.forEach((val) => this.tempSelectedFilters[field].add(val));
    } else {
      this.tempSelectedFilters[field].clear();
    }
    this.updateIndeterminateState(field);
  }

  isAllFilterSelected(field: string): boolean {
    return (
      this.columnFilters[field].length > 0 &&
      this.tempSelectedFilters[field].size === this.columnFilters[field].length
    );
  }
  isIndeterminate(field: string): boolean {
    const total = this.columnFilters[field].length;
    const selected = this.tempSelectedFilters[field].size;
    return selected > 0 && selected < total;
  }
  updateIndeterminateState(field: string): void {
    const total = this.columnFilters[field]?.length || 0;
    const selected = this.tempSelectedFilters[field]?.size || 0;

    // Set indeterminate to true if some but not all are selected
    this.indeterminateStates = this.indeterminateStates || {};
    this.indeterminateStates[field] = selected > 0 && selected < total;
  }

  filteredColumns() {
    if (!this.columnSearch) return this.columns;
    return this.columns.filter((col) =>
      col.label.toLowerCase().includes(this.columnSearch.toLowerCase())
    );
  }

  onToggleColumnPanel(): void {
    console.log('test');

    this.showColumnPanel = !this.showColumnPanel;
  }

  // onPageChange(page: number): void {
  //   this.pageChange.emit(page); // Emit the page change to the parent
  // }
}
