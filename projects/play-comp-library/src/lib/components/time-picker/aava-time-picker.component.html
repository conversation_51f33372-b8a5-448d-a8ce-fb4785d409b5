<div class="time-picker-container" [ngClass]="'size-' + size">
  <div class="time-picker-input" >
    <div class="time-display" *ngIf="!isFocused" (click)="onDisplayClick($event)">
      {{ displayTime }}
    </div>



    <!-- Scrolling time picker interface -->
    <div class="time-scroll-container" *ngIf="isFocused">
      <!-- Inline input overlay for scroll mode -->
      <div class="inline-input-overlay" *ngIf="showInlineInput"
           [style.top.px]="inlineInputPosition.top"
           [style.left.px]="inlineInputPosition.left"
           [style.width.px]="inlineInputPosition.width">
        <input
          type="text"
          class="inline-scroll-input"
          [value]="inlineInputValue"
          (input)="onInlineInputChange($event)"
          (keydown)="onInlineInputKeyPress($event)"
          (blur)="onInlineInputBlur()"
          [placeholder]="inlineInputType === 'hours' ? 'HH' : inlineInputType === 'minutes' ? 'MM' : 'AM/PM'"
          maxlength="2">
      </div>

      <!-- Hours scroll -->
      <div class="time-scroll-column">
        <div class="scroll-area" #hoursScroll (scroll)="onScrollEvent($event, 'hours')">
          <div
            *ngFor="let hour of hoursList"
            class="time-item"
            [class.selected]="hour === centeredHour && hour !== ''"
            [class.padding-item]="hour === ''"
            [class.hidden-item]="showInlineInput && inlineInputType === 'hours' && hour === clickedItemValue"
            (click)="hour !== '' ? onTimeItemClick($event, 'hours', hour) : null"
            [style.cursor]="hour !== '' ? 'pointer' : 'default'">
            {{ hour }}
          </div>
        </div>
      </div>

      <span class="separator">:</span>

      <!-- Minutes scroll -->
      <div class="time-scroll-column">
        <div class="scroll-area" #minutesScroll (scroll)="onScrollEvent($event, 'minutes')">
          <div
            *ngFor="let minute of minutesList"
            class="time-item"
            [class.selected]="minute === centeredMinute && minute !== ''"
            [class.padding-item]="minute === ''"
            [class.hidden-item]="showInlineInput && inlineInputType === 'minutes' && minute === clickedItemValue"
            (click)="minute !== '' ? onTimeItemClick($event, 'minutes', minute) : null"
            [style.cursor]="minute !== '' ? 'pointer' : 'default'">
            {{ minute }}
          </div>
        </div>
      </div>

      <!-- Period scroll -->
      <div class="time-scroll-column period-column">
        <div class="scroll-area" #periodScroll (scroll)="onScrollEvent($event, 'period')">
          <div
            *ngFor="let p of periodList"
            class="time-item"
            [class.selected]="p === centeredPeriod && p !== ''"
            [class.padding-item]="p === ''"
            [class.hidden-item]="showInlineInput && inlineInputType === 'period' && p === clickedItemValue"
            (click)="p !== '' ? onTimeItemClick($event, 'period', p) : null"
            [style.cursor]="p !== '' ? 'pointer' : 'default'">
            {{ p }}
          </div>
        </div>
      </div>
    </div>

    <div class="icon-wrapper" (click)="onIconClick($event)" tabindex="0">
      <ava-icon [iconName]="'clock'" [iconColor]="getIconColor" [iconSize]="computedIconSize"></ava-icon>
    </div>
  </div>
</div>