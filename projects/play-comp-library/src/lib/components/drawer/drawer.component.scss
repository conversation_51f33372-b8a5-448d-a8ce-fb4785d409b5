.drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  visibility: hidden;
  z-index: 1000;
}

.drawer-container.open {
  visibility: visible;
}

.backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.drawer-container.open .backdrop {
  opacity: 1;
}

.drawer {
  position: absolute;
  top: 0;
  height: 100%;
  width: 300px;
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease-in-out;
}

.drawer.right {
  right: 0;
  transform: translateX(100%);
}

.drawer.left {
  left: 0;
  transform: translateX(-100%);
}

.drawer-container.open .drawer.right {
  transform: translateX(0);
}

.drawer-container.open .drawer.left {
  transform: translateX(0);
}