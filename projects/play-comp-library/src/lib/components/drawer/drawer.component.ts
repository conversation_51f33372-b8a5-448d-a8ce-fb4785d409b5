import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'ava-drawer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './drawer.component.html',
  styleUrls: ['./drawer.component.css']
})
export class AvaDrawerComponent {
  /**
   * Controls the visibility of the drawer.
   */
  @Input() isOpen = false;

  /**
   * Determines the position of the drawer ('left' or 'right').
   */
  @Input() position: 'left' | 'right' = 'right';

  /**
   * Emits an event when the drawer is requested to be closed.
   */
  @Output() drawerClose = new EventEmitter<void>();

  /**
   * Closes the drawer by emitting the drawerClose event.
   */
  closeDrawer(): void {
    this.drawerClose.emit();
  }
}