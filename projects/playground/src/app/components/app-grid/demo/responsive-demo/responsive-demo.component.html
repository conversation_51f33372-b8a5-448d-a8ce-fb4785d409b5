<div class="responsive-demo">
  <div class="demo-content">
    <ava-grid-demo-card
      title="Breakpoint System"
      description="The grid uses five responsive breakpoints: xs, sm, md, lg, and xl."
    >
      <div class="breakpoint-info">
        <div class="breakpoint-item"><strong>xs:</strong> 0px+</div>
        <div class="breakpoint-item"><strong>sm:</strong> 576px+</div>
        <div class="breakpoint-item"><strong>md:</strong> 768px+</div>
        <div class="breakpoint-item"><strong>lg:</strong> 992px+</div>
        <div class="breakpoint-item"><strong>xl:</strong> 1200px+</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Basic Responsive Example"
      description="Columns stack on mobile and expand on larger screens. Resize your browser to see the effect."
    >
      <div class="row">
        <div class="col-12 col-sm-6 col-md-4 demo-col">
          col-12 col-sm-6 col-md-4
        </div>
        <div class="col-12 col-sm-6 col-md-4 demo-col">
          col-12 col-sm-6 col-md-4
        </div>
        <div class="col-12 col-sm-12 col-md-4 demo-col">
          col-12 col-sm-12 col-md-4
        </div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Complex Responsive Layout"
      description="Create sophisticated layouts by combining different column classes."
    >
      <div class="row">
        <div class="col-12 col-md-8 demo-col">col-12 col-md-8</div>
        <div class="col-12 col-md-4 demo-col">col-12 col-md-4</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Card Grid Layout"
      description="A common use case for a responsive grid is a card layout."
    >
      <div class="row">
        <div class="col-12 col-sm-6 col-lg-3 demo-col">Card</div>
        <div class="col-12 col-sm-6 col-lg-3 demo-col">Card</div>
        <div class="col-12 col-sm-6 col-lg-3 demo-col">Card</div>
        <div class="col-12 col-sm-6 col-lg-3 demo-col">Card</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Responsive Offsets"
      description="Use offset classes to move columns to the right."
    >
      <div class="row">
        <div class="col-md-8 offset-md-2 col-lg-4 offset-lg-4 demo-col">
          Centered Column
        </div>
      </div>
    </ava-grid-demo-card>
  </div>
</div>
