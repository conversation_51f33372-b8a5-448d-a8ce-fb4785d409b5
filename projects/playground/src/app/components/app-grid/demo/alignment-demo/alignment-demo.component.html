<div class="alignment-demo">
  <div class="demo-content">
    <ava-grid-demo-card
      title="Vertical Alignment (Align Items)"
      description="Control the vertical alignment of items in a row with .align-items-..."
    >
      <div class="row align-items-start demo-alignment-row">
        <div class="col-4 demo-col">Item</div>
        <div class="col-4 demo-col" style="height: 100px">Item</div>
        <div class="col-4 demo-col">Item</div>
      </div>
      <div class="row align-items-center demo-alignment-row">
        <div class="col-4 demo-col">Item</div>
        <div class="col-4 demo-col" style="height: 100px">Item</div>
        <div class="col-4 demo-col">Item</div>
      </div>
      <div class="row align-items-end demo-alignment-row">
        <div class="col-4 demo-col">Item</div>
        <div class="col-4 demo-col" style="height: 100px">Item</div>
        <div class="col-4 demo-col">Item</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Horizontal Alignment (Justify Content)"
      description="Control the horizontal alignment of columns with .justify-content-..."
    >
      <div class="row justify-content-start">
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
      </div>
      <div class="row justify-content-center">
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
      </div>
      <div class="row justify-content-end">
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
      </div>
      <div class="row justify-content-between">
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
      </div>
      <div class="row justify-content-around">
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
      </div>
    </ava-grid-demo-card>
  </div>
</div>
