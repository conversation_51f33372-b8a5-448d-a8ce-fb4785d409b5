.otp-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.hero-section {
  text-align: center;
  margin-bottom: 4rem;
  padding: 3rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;

  h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }
}

.demo-section {
  margin-bottom: 4rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .section-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;

    h2 {
      color: #1e293b;
      font-size: 1.875rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    p {
      color: #64748b;
      font-size: 1.125rem;
      margin: 0;
    }
  }

  .demo-content {
    padding: 2rem;
  }

  .code-example {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1.5rem;
    margin: 0;
    border-top: 1px solid #e2e8f0;

    pre {
      margin: 0;
      overflow-x: auto;

      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        line-height: 1.5;
        white-space: pre;
      }
    }
  }
}

.example-item {
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  h4 {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .value-display {
    margin-top: 1rem;
    padding: 0.75rem;
    background: #f1f5f9;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    color: #475569;
    word-break: break-all;
  }

  .clear-btn {
    margin-top: 0.5rem;
    padding: 0.5rem 1rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.2s ease;

    &:hover {
      background: #2563eb;
    }

    &:active {
      background: #1d4ed8;
    }
  }
}

// Grid layouts
.variants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.sizes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.lengths-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

// Responsive design
@media (max-width: 768px) {
  .otp-demo-container {
    padding: 1rem;
  }

  .hero-section {
    margin-bottom: 2rem;
    padding: 2rem 1rem;

    h1 {
      font-size: 2rem;
    }

    .hero-description {
      font-size: 1rem;
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    .section-header {
      padding: 1.5rem 1rem 1rem;

      h2 {
        font-size: 1.5rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .demo-content {
      padding: 1rem;
    }

    .code-example {
      padding: 1rem;

      pre code {
        font-size: 0.75rem;
      }
    }
  }

  .variants-grid,
  .sizes-grid,
  .lengths-grid,
  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .example-item {
    padding: 1rem;
  }
}

// Animation for demo items
.example-item {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Focus styles for accessibility
.clear-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

// Print styles
@media print {
  .otp-demo-container {
    padding: 0;
  }

  .hero-section {
    background: none !important;
    color: black !important;
    box-shadow: none !important;
  }

  .demo-section {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
    break-inside: avoid;
  }

  .code-example {
    background: #f5f5f5 !important;
    color: black !important;
  }
}
