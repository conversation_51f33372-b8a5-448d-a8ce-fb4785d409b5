.test-accordion-container {
    max-width: 1200px;
    padding: 2rem;
    min-height: 100vh;

    .header-section {
        text-align: center;
        margin-bottom: 3rem;

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        p {
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
    }

    .accordion-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, max-content));
        justify-content: start;
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .accordion-section {
        background: white;
        border-radius: 16px;
        padding: 2rem;

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-description {
            font-size: 0.9rem;
            color: #64748b;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .accordion-wrapper {
            margin-bottom: 1.5rem;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    ava-accordion {
        margin-bottom: 1rem;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .dashboard-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        margin: 1rem 0;

        h5 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
        }

        p {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-1px);
            }
        }
    }

    .rich-header-content {
        h4 {
            margin: 0;
            color: #1e293b;
            font-weight: 600;
            font-size: 1.1rem;
        }

        small {
            color: #64748b;
            font-size: 0.85rem;
            display: block;
            margin-top: 0.25rem;
        }
    }

    .settings-content {
        background: #f8fafc;
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 4px solid #3b82f6;

        h4 {
            color: #1e293b;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        p {
            color: #64748b;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        ul {
            margin: 0;
            padding-left: 1.5rem;

            li {
                color: #475569;
                margin-bottom: 0.5rem;
                line-height: 1.5;
            }
        }
    }

    @media (max-width: 768px) {
        padding: 1rem;

        .header-section {
            h1 {
                font-size: 2rem;
            }
        }

        .accordion-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .accordion-section {
            padding: 1.5rem;
        }
    }

    @media (max-width: 480px) {
        .accordion-section {
            padding: 1rem;
        }

        .header-section {
            h1 {
                font-size: 1.75rem;
            }
        }
    }
}