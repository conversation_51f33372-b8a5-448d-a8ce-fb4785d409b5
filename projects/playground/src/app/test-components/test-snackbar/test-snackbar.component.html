<div class="t-container">
  <!--Success snackbar-->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>1. Snackbar Success Variants</h2>
        <div class="demo-container">
          <div class="control-group">
            <div class="control-row">
              <ava-button
                label="Success Large"
                variant="success"
                (userClick)="showLargeSuccess()"
              ></ava-button>
              <ava-button
                label="Success Medium"
                variant="success"
                (userClick)="showMediumSuccess()"
              ></ava-button>
              <ava-button
                label="Success Small"
                variant="success"
                (userClick)="showSmallSuccess()"
              ></ava-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>1. Snackbar Warning Variants</h2>
        <div class="demo-container">
          <div class="control-group">
            <div class="control-row">
              <ava-button
                label="Warning Large"
                variant="warning"
                (userClick)="showLargeWarning()"
              ></ava-button>
              <ava-button
                label="Warning Medium"
                variant="warning"
                (userClick)="showMediumWarning()"
              ></ava-button>
              <ava-button
                label="Warning Small"
                variant="warning"
                (userClick)="showSmallWarning()"
              ></ava-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>1. Snackbar Error Variants</h2>
        <div class="demo-container">
          <div class="control-group">
            <div class="control-row">
              <ava-button
                label="Error Large"
                variant="danger"
                (userClick)="showLargeError()"
              ></ava-button>
              <ava-button
                label="Error Medium"
                variant="danger"
                (userClick)="showMediumError()"
              ></ava-button>
              <ava-button
                label="Error Small"
                variant="danger"
                (userClick)="showSmallError()"
              ></ava-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>1. Snackbar Info Variants</h2>
        <div class="demo-container">
          <div class="control-group">
            <div class="control-row">
              <ava-button
                label="Info Large"
                variant="info"
                (userClick)="showLargeInfo()"
              ></ava-button>
              <ava-button
                label="Info Medium"
                variant="info"
                (userClick)="showMediumInfo()"
              ></ava-button>
              <ava-button
                label="Info Small"
                variant="info"
                (userClick)="showSmallInfo()"
              ></ava-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
